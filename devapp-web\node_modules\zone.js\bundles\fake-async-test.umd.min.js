"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},__assign.apply(this,arguments)},__spreadArray=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var i,n=0,s=t.length;n<s;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))};
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */
!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e,t="object"==typeof window&&window||"object"==typeof self&&self||globalThis.global,r=t.Date;function i(){if(0===arguments.length){var e=new r;return e.setTime(i.now()),e}var t=Array.prototype.slice.call(arguments);return new(r.bind.apply(r,__spreadArray([void 0],t,!1)))}i.now=function(){var e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():r.now.apply(this,arguments)},i.UTC=r.UTC,i.parse=r.parse;var n=function(){},s=function(){function i(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=r.now(),this._currentTickRequeuePeriodicEntries=[]}return i.getNextId=function(){var r=e.nativeSetTimeout.call(t,n,0);return e.nativeClearTimeout.call(t,r),"number"==typeof r?r:i.nextNodeJSId++},i.prototype.getCurrentTickTime=function(){return this._currentTickTime},i.prototype.getFakeSystemTime=function(){return this._currentFakeBaseSystemTime+this._currentTickTime},i.prototype.setFakeBaseSystemTime=function(e){this._currentFakeBaseSystemTime=e},i.prototype.getRealSystemTime=function(){return r.now()},i.prototype.scheduleFunction=function(e,t,r){var n=(r=__assign({args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1},r)).id<0?i.nextId:r.id;i.nextId=i.getNextId();var s={endTime:this._currentTickTime+t,id:n,func:e,args:r.args,delay:t,isPeriodic:r.isPeriodic,isRequestAnimationFrame:r.isRequestAnimationFrame};r.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(s);for(var o=0;o<this._schedulerQueue.length&&!(s.endTime<this._schedulerQueue[o].endTime);o++);return this._schedulerQueue.splice(o,0,s),n},i.prototype.removeScheduledFunctionWithId=function(e){for(var t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}},i.prototype.removeAll=function(){this._schedulerQueue=[]},i.prototype.getTimerCount=function(){return this._schedulerQueue.length},i.prototype.tickToNext=function(e,t,r){void 0===e&&(e=1),this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,r)},i.prototype.tick=function(e,r,i){void 0===e&&(e=0);var n=this._currentTickTime+e,s=0,o=(i=Object.assign({processNewMacroTasksSynchronously:!0},i)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===o.length&&r)r(e);else{for(;o.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(n<o[0].endTime));){var a=o.shift();if(!i.processNewMacroTasksSynchronously){var c=this._schedulerQueue.indexOf(a);c>=0&&this._schedulerQueue.splice(c,1)}if(s=this._currentTickTime,this._currentTickTime=a.endTime,r&&r(this._currentTickTime-s),!a.func.apply(t,a.isRequestAnimationFrame?[this._currentTickTime]:a.args))break;i.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((function(e){for(var t=0;t<o.length&&!(e.endTime<o[t].endTime);t++);o.splice(t,0,e)}))}s=this._currentTickTime,this._currentTickTime=n,r&&r(this._currentTickTime-s)}},i.prototype.flushOnlyPendingTimers=function(e){if(0===this._schedulerQueue.length)return 0;var t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t},i.prototype.flush=function(e,t,r){return void 0===e&&(e=20),void 0===t&&(t=!1),t?this.flushPeriodic(r):this.flushNonPeriodic(e,r)},i.prototype.flushPeriodic=function(e){if(0===this._schedulerQueue.length)return 0;var t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t},i.prototype.flushNonPeriodic=function(e,r){for(var i=this._currentTickTime,n=0,s=0;this._schedulerQueue.length>0;){if(++s>e)throw new Error("flush failed after reaching the limit of "+e+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((function(e){return!e.isPeriodic&&!e.isRequestAnimationFrame})).length)break;var o=this._schedulerQueue.shift();if(n=this._currentTickTime,this._currentTickTime=o.endTime,r&&r(this._currentTickTime-n),!o.func.apply(t,o.args))break}return this._currentTickTime-i},i.nextNodeJSId=1,i.nextId=-1,i}(),o=function(){function n(e,r,i){void 0===r&&(r=!1),this._scheduler=new s,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.trackPendingRequestAnimationFrame=r,this.macroTaskOptions=i,this.name="fakeAsyncTestZone for "+e,this.macroTaskOptions||(this.macroTaskOptions=t[Zone.__symbol__("FakeAsyncTestMacroTask")])}return n.assertInZone=function(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")},n.prototype._fnAndFlush=function(e,r){var i=this;return function(){for(var n=[],s=0;s<arguments.length;s++)n[s]=arguments[s];return e.apply(t,n),null===i._lastError?(null!=r.onSuccess&&r.onSuccess.apply(t),i.flushMicrotasks()):null!=r.onError&&r.onError.apply(t),null===i._lastError}},n._removeTimer=function(e,t){var r=e.indexOf(t);r>-1&&e.splice(r,1)},n.prototype._dequeueTimer=function(e){var t=this;return function(){n._removeTimer(t.pendingTimers,e)}},n.prototype._requeuePeriodicTimer=function(e,t,r,i){var n=this;return function(){-1!==n.pendingPeriodicTimers.indexOf(i)&&n._scheduler.scheduleFunction(e,t,{args:r,isPeriodic:!0,id:i,isRequeuePeriodic:!0})}},n.prototype._dequeuePeriodicTimer=function(e){var t=this;return function(){n._removeTimer(t.pendingPeriodicTimers,e)}},n.prototype._setTimeout=function(e,t,r,i){void 0===i&&(i=!0);var n=this._dequeueTimer(s.nextId),o=this._fnAndFlush(e,{onSuccess:n,onError:n}),a=this._scheduler.scheduleFunction(o,t,{args:r,isRequestAnimationFrame:!i});return i&&this.pendingTimers.push(a),a},n.prototype._clearTimeout=function(e){n._removeTimer(this.pendingTimers,e),this._scheduler.removeScheduledFunctionWithId(e)},n.prototype._setInterval=function(e,t,r){var i=s.nextId,n={onSuccess:null,onError:this._dequeuePeriodicTimer(i)},o=this._fnAndFlush(e,n);return n.onSuccess=this._requeuePeriodicTimer(o,t,r,i),this._scheduler.scheduleFunction(o,t,{args:r,isPeriodic:!0}),this.pendingPeriodicTimers.push(i),i},n.prototype._clearInterval=function(e){n._removeTimer(this.pendingPeriodicTimers,e),this._scheduler.removeScheduledFunctionWithId(e)},n.prototype._resetLastErrorAndThrow=function(){var e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e},n.prototype.getCurrentTickTime=function(){return this._scheduler.getCurrentTickTime()},n.prototype.getFakeSystemTime=function(){return this._scheduler.getFakeSystemTime()},n.prototype.setFakeBaseSystemTime=function(e){this._scheduler.setFakeBaseSystemTime(e)},n.prototype.getRealSystemTime=function(){return this._scheduler.getRealSystemTime()},n.patchDate=function(){t[Zone.__symbol__("disableDatePatching")]||t.Date!==i&&(t.Date=i,i.prototype=r.prototype,n.checkTimerPatch())},n.resetDate=function(){t.Date===i&&(t.Date=r)},n.checkTimerPatch=function(){if(!e)throw new Error("Expected timers to have been patched.");t.setTimeout!==e.setTimeout&&(t.setTimeout=e.setTimeout,t.clearTimeout=e.clearTimeout),t.setInterval!==e.setInterval&&(t.setInterval=e.setInterval,t.clearInterval=e.clearInterval)},n.prototype.lockDatePatch=function(){this.patchDateLocked=!0,n.patchDate()},n.prototype.unlockDatePatch=function(){this.patchDateLocked=!1,n.resetDate()},n.prototype.tickToNext=function(e,t,r){void 0===e&&(e=1),void 0===r&&(r={processNewMacroTasksSynchronously:!0}),e<=0||(n.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(e,t,r),null!==this._lastError&&this._resetLastErrorAndThrow())},n.prototype.tick=function(e,t,r){void 0===e&&(e=0),void 0===r&&(r={processNewMacroTasksSynchronously:!0}),n.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(e,t,r),null!==this._lastError&&this._resetLastErrorAndThrow()},n.prototype.flushMicrotasks=function(){var e=this;for(n.assertInZone();this._microtasks.length>0;){var t=this._microtasks.shift();t.func.apply(t.target,t.args)}(null!==e._lastError||e._uncaughtPromiseErrors.length)&&e._resetLastErrorAndThrow()},n.prototype.flush=function(e,t,r){n.assertInZone(),this.flushMicrotasks();var i=this._scheduler.flush(e,t,r);return null!==this._lastError&&this._resetLastErrorAndThrow(),i},n.prototype.flushOnlyPendingTimers=function(e){n.assertInZone(),this.flushMicrotasks();var t=this._scheduler.flushOnlyPendingTimers(e);return null!==this._lastError&&this._resetLastErrorAndThrow(),t},n.prototype.removeAllTimers=function(){n.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]},n.prototype.getTimerCount=function(){return this._scheduler.getTimerCount()+this._microtasks.length},n.prototype.onScheduleTask=function(e,t,r,i){switch(i.type){case"microTask":var n=i.data&&i.data.args,s=void 0;if(n){var o=i.data.cbIdx;"number"==typeof n.length&&n.length>o+1&&(s=Array.prototype.slice.call(n,o+1))}this._microtasks.push({func:i.invoke,args:s,target:i.data&&i.data.target});break;case"macroTask":switch(i.source){case"setTimeout":i.data.handleId=this._setTimeout(i.invoke,i.data.delay,Array.prototype.slice.call(i.data.args,2));break;case"setImmediate":i.data.handleId=this._setTimeout(i.invoke,0,Array.prototype.slice.call(i.data.args,1));break;case"setInterval":i.data.handleId=this._setInterval(i.invoke,i.data.delay,Array.prototype.slice.call(i.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+i.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":i.data.handleId=this._setTimeout(i.invoke,16,i.data.args,this.trackPendingRequestAnimationFrame);break;default:var a=this.findMacroTaskOption(i);if(a){var c=i.data&&i.data.args,u=c&&c.length>1?c[1]:0,h=a.callbackArgs?a.callbackArgs:c;a.isPeriodic?(i.data.handleId=this._setInterval(i.invoke,u,h),i.data.isPeriodic=!0):i.data.handleId=this._setTimeout(i.invoke,u,h);break}throw new Error("Unknown macroTask scheduled in fake async test: "+i.source)}break;case"eventTask":i=e.scheduleTask(r,i)}return i},n.prototype.onCancelTask=function(e,t,r,i){switch(i.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(i.data.handleId);case"setInterval":return this._clearInterval(i.data.handleId);default:var n=this.findMacroTaskOption(i);if(n){var s=i.data.handleId;return n.isPeriodic?this._clearInterval(s):this._clearTimeout(s)}return e.cancelTask(r,i)}},n.prototype.onInvoke=function(e,t,r,i,s,o,a){try{return n.patchDate(),e.invoke(r,i,s,o,a)}finally{this.patchDateLocked||n.resetDate()}},n.prototype.findMacroTaskOption=function(e){if(!this.macroTaskOptions)return null;for(var t=0;t<this.macroTaskOptions.length;t++){var r=this.macroTaskOptions[t];if(r.source===e.source)return r}return null},n.prototype.onHandleError=function(e,t,r,i){return this._lastError=i,!1},n}(),a=null;function c(){return Zone&&Zone.ProxyZoneSpec}var u=null;function h(){var e,t;a&&a.unlockDatePatch(),a=null,null===(t=null===(e=c())||void 0===e?void 0:e.get())||void 0===t||t.resetDelegate(),null==u||u.resetDelegate()}function l(e,t){void 0===t&&(t={});var r=t.flush,i=void 0===r||r,n=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=c();if(!n)throw new Error("ProxyZoneSpec is needed for the fakeAsync() test helper but could not be found. Make sure that your environment includes zone-testing.js");var s=n.assertPresent();if(Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!a){var o=Zone&&Zone.FakeAsyncTestZoneSpec;if(s.getDelegate()instanceof o)throw new Error("fakeAsync() calls can not be nested");a=new o}var u=void 0,l=s.getDelegate();s.setDelegate(a),a.lockDatePatch();try{u=e.apply(this,t),i?a.flush(20,!0):_()}finally{s.setDelegate(l)}if(!i){if(a.pendingPeriodicTimers.length>0)throw new Error("".concat(a.pendingPeriodicTimers.length," ")+"periodic timer(s) still in the queue.");if(a.pendingTimers.length>0)throw new Error("".concat(a.pendingTimers.length," timer(s) still in the queue."))}return u}finally{h()}};return n.isFakeAsync=!0,n}function d(){if(null==a&&null==(a=Zone.current.get("FakeAsyncTestZoneSpec")))throw new Error("The code should be running in the fakeAsync zone to call this function");return a}function m(e,t){void 0===e&&(e=0),void 0===t&&(t=!1),d().tick(e,null,t)}function f(e){return d().flush(e)}function p(){d().pendingPeriodicTimers.length=0}function T(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var i=c();if(void 0===i)throw new Error("ProxyZoneSpec is needed for the withProxyZone() test helper but could not be found. Make sure that your environment includes zone-testing.js");return(void 0!==i.get()?Zone.current:function n(){var e=c();if(void 0===e)throw new Error("ProxyZoneSpec is needed for withProxyZone but could not be found. Make sure that your environment includes zone-testing.js");return null===u&&(u=new e),Zone.root.fork(u)}()).run(e,this,t)}}function _(){d().flushMicrotasks()}!function y(r){r.FakeAsyncTestZoneSpec=o,r.__load_patch("fakeasync",(function(e,t,r){t[r.symbol("fakeAsyncTest")]={resetFakeAsyncZone:h,flushMicrotasks:_,discardPeriodicTasks:p,tick:m,flush:f,fakeAsync:l,withProxyZone:T}}),!0),e={setTimeout:t.setTimeout,setInterval:t.setInterval,clearTimeout:t.clearTimeout,clearInterval:t.clearInterval,nativeSetTimeout:t[r.__symbol__("setTimeout")],nativeClearTimeout:t[r.__symbol__("clearTimeout")]},s.nextId=s.getNextId()}(Zone)}));