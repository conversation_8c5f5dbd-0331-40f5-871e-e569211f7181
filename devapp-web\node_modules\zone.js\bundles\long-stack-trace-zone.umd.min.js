"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var a,n=1,r=arguments.length;n<r;n++)for(var e in a=arguments[n])Object.prototype.hasOwnProperty.call(a,e)&&(t[e]=a[e]);return t},__assign.apply(this,arguments)};
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){!function t(a){var n={},r="__creationTrace__",e="STACKTRACE TRACKING",c="__SEP_TAG__",i=c+"@[native]",o=function o(){this.error=h(),this.timestamp=new Date};function s(){return new Error(e)}function _(){try{throw s()}catch(t){return t}}var f=s(),u=_(),h=f.stack?s:u.stack?_:s;function l(t){return t.stack?t.stack.split("\n"):[]}function g(t,a){for(var r=l(a),e=0;e<r.length;e++)n.hasOwnProperty(r[e])||t.push(r[e])}function k(t,a){var n=[a?a.trim():""];if(t)for(var r=(new Date).getTime(),e=0;e<t.length;e++){var o=t[e],s=o.timestamp,_="____________________Elapsed ".concat(r-s.getTime()," ms; At: ").concat(s);_=_.replace(/[^\w\d]/g,"_"),n.push(i.replace(c,_)),g(n,o.error),r=s.getTime()}return n.join("\n")}function T(){return Error.stackTraceLimit>0}function d(t,a){a>0&&(t.push(l((new o).error)),d(t,a-1))}a.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(t){if(t){var n=t[a.__symbol__("currentTaskTrace")];return n?k(n,t.stack):t.stack}},onScheduleTask:function(t,n,e,c){if(T()){var i=a.currentTask,s=i&&i.data&&i.data[r]||[];(s=[new o].concat(s)).length>this.longStackTraceLimit&&(s.length=this.longStackTraceLimit),c.data||(c.data={}),"eventTask"===c.type&&(c.data=__assign({},c.data)),c.data[r]=s}return t.scheduleTask(e,c)},onHandleError:function(t,n,e,c){if(T()){var i=a.currentTask||c.task;if(c instanceof Error&&i){var o=k(i.data&&i.data[r],c.stack);try{c.stack=c.longStack=o}catch(t){}}}return t.handleError(e,c)}},function p(){if(T()){var t=[];d(t,2);for(var a=t[0],r=t[1],o=0;o<a.length;o++)if(-1==(_=a[o]).indexOf(e)){var s=_.match(/^\s*at\s+/);if(s){i=s[0]+c+" (http://localhost)";break}}for(o=0;o<a.length;o++){var _;if((_=a[o])!==r[o])break;n[_]=!0}}}()}(Zone)}));