# Multi-stage Dockerfile for DevApp Development Environment
# Supports Angular 20 + Spring Boot 3.4.1 development

FROM mcr.microsoft.com/devcontainers/base:ubuntu-24.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
ENV MAVEN_HOME=/opt/maven
ENV NODE_VERSION=24
ENV PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$PATH

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Basic tools
    curl \
    wget \
    git \
    unzip \
    zip \
    # Java 21
    openjdk-21-jdk \
    # Build tools
    build-essential \
    # Network tools
    net-tools \
    # Process tools
    htop \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Maven 3.9.x
RUN wget -q https://archive.apache.org/dist/maven/maven-3/3.9.9/binaries/apache-maven-3.9.9-bin.tar.gz \
    && tar -xzf apache-maven-3.9.9-bin.tar.gz -C /opt \
    && ln -s /opt/apache-maven-3.9.9 /opt/maven \
    && rm apache-maven-3.9.9-bin.tar.gz

# Install Node.js 24.x using NodeSource repository
RUN curl -fsSL https://deb.nodesource.com/setup_24.x | bash - \
    && apt-get install -y nodejs

# Install Angular CLI globally
RUN npm install -g @angular/cli@20

# Install useful development tools
RUN npm install -g \
    typescript \
    ts-node \
    nodemon \
    concurrently

# Create workspace directory
RUN mkdir -p /workspace

# Set up vscode user
RUN groupadd --gid 1000 vscode \
    && useradd --uid 1000 --gid vscode --shell /bin/bash --create-home vscode \
    && echo vscode ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/vscode \
    && chmod 0440 /etc/sudoers.d/vscode

# Change ownership of workspace
RUN chown -R vscode:vscode /workspace

# Switch to vscode user
USER vscode

# Set working directory
WORKDIR /workspace

# Verify installations
RUN java -version && mvn -version && node -v && npm -v && ng version
