# Multi-stage Dockerfile for DevApp Development Environment
# Supports Angular 20 + Spring Boot 3.4.1 development

FROM mcr.microsoft.com/devcontainers/java:21-bookworm

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_VERSION=24

# Install additional system dependencies
RUN apt-get update && apt-get install -y \
    # Network tools
    net-tools \
    # Process tools
    htop \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 24.x using NodeSource repository
RUN curl -fsSL https://deb.nodesource.com/setup_24.x | bash - \
    && apt-get install -y nodejs

# Install Angular CLI globally
RUN npm install -g @angular/cli@20

# Install useful development tools
RUN npm install -g \
    typescript \
    ts-node \
    nodemon \
    concurrently

# Create workspace directory
RUN mkdir -p /workspace

# Change ownership of workspace to vscode user (user already exists in base image)
RUN chown -R vscode:vscode /workspace

# Switch to vscode user
USER vscode

# Set working directory
WORKDIR /workspace

# Verify installations
RUN java -version && mvn -version && node -v && npm -v && ng version
