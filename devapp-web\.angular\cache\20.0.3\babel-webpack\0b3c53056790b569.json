{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./user.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./user.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { UserService } from '../services/user.service';\nlet UserComponent = class UserComponent {\n  constructor(userService) {\n    this.userService = userService;\n    this.users = [];\n    this.newUser = {\n      name: ''\n    };\n  }\n  ngOnInit() {\n    this.userService.getAllUsers().subscribe(data => {\n      this.users = data;\n    });\n  }\n  createUser() {\n    this.userService.createUser(this.newUser).subscribe(user => {\n      this.users.push(user);\n      this.newUser = {\n        name: ''\n      };\n    });\n  }\n  static #_ = this.ctorParameters = () => [{\n    type: UserService\n  }];\n};\nUserComponent = __decorate([Component({\n  selector: 'app-user',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  styles: [__NG_CLI_RESOURCE__1]\n})], UserComponent);\nexport { UserComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "UserService", "UserComponent", "constructor", "userService", "users", "newUser", "name", "ngOnInit", "getAllUsers", "subscribe", "data", "createUser", "user", "push", "_", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\dev\\workspace\\devapp\\devapp-web\\src\\app\\user\\user.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { UserService } from '../services/user.service';\r\nimport { User } from '../models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-user',\r\n  templateUrl: './user.component.html',\r\n  styleUrls: ['./user.component.css'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule]\r\n})\r\nexport class UserComponent implements OnInit {\r\n  users: User[] = [];\r\n  newUser: User = { name: '' };\r\n\r\n  constructor(private userService: UserService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.userService.getAllUsers().subscribe(data => {\r\n      this.users = data;\r\n    });\r\n  }\r\n\r\n  createUser(): void {\r\n    this.userService.createUser(this.newUser).subscribe(user => {\r\n      this.users.push(user);\r\n      this.newUser = { name: '' };\r\n    });\r\n  }\r\n}"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,0BAA0B;AAU/C,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAIxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAH/B,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,OAAO,GAAS;MAAEC,IAAI,EAAE;IAAE,CAAE;EAEoB;EAEhDC,QAAQA,CAAA;IACN,IAAI,CAACJ,WAAW,CAACK,WAAW,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MAC9C,IAAI,CAACN,KAAK,GAAGM,IAAI;IACnB,CAAC,CAAC;EACJ;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACR,WAAW,CAACQ,UAAU,CAAC,IAAI,CAACN,OAAO,CAAC,CAACI,SAAS,CAACG,IAAI,IAAG;MACzD,IAAI,CAACR,KAAK,CAACS,IAAI,CAACD,IAAI,CAAC;MACrB,IAAI,CAACP,OAAO,GAAG;QAAEC,IAAI,EAAE;MAAE,CAAE;IAC7B,CAAC,CAAC;EACJ;EAAC,QAAAQ,CAAA,G;;;;AAjBUb,aAAa,GAAAc,UAAA,EAPzBlB,SAAS,CAAC;EACTmB,QAAQ,EAAE,UAAU;EACpBC,QAAA,EAAAC,oBAAoC;EAEpCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtB,YAAY,EAAEC,WAAW,CAAC;;CACrC,CAAC,C,EACWE,aAAa,CAkBzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}