import { Component, OnInit } from '@angular/core';
import { UserService } from '../services/user.service';
import { User } from '../models/user.model';

@Component({
  selector: 'app-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.css'],
  standalone: false
})
export class UserComponent implements OnInit {
  users: User[] = [];
  newUser: User = { name: '' };

  constructor(private userService: UserService) { }

  ngOnInit(): void {
    this.userService.getAllUsers().subscribe(data => {
      this.users = data;
    });
  }

  createUser(): void {
    this.userService.createUser(this.newUser).subscribe(user => {
      this.users.push(user);
      this.newUser = { name: '' };
    });
  }
}