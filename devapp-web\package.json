{"name": "devapp-web", "version": "1.0.0", "description": "DevApp Web", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test --watch=false --browsers=ChromeHeadless", "e2e": "cypress run", "build-prod": "ng build --configuration production", "start-prod": "ng serve --configuration production", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/animations": "^20.0.4", "@angular/cdk": "^20.0.3", "@angular/common": "^20.0.4", "@angular/compiler": "^20.0.4", "@angular/core": "^20.0.4", "@angular/forms": "^20.0.4", "@angular/material": "^20.0.3", "@angular/platform-browser": "^20.0.4", "@angular/platform-browser-dynamic": "^20.0.4", "@angular/router": "^20.0.4", "devapp-web": "file:"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.3", "@angular/cli": "^20.0.3", "@angular/compiler-cli": "^20.0.4", "@types/jasmine": "~4.3.0", "@types/node": "^22.15.32", "codelyzer": "^6.0.2", "cypress": "^13.10.0", "eslint": "^8.38.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsdoc": "^40.0.0", "eslint-plugin-prefer-arrow": "^1.2.3", "jasmine-core": "~4.5.0", "jasmine-spec-reporter": "~6.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.0.0", "karma-jasmine-html-reporter": "^2.0.0", "puppeteer": "^24.10.0", "rxjs": "~7.8.0", "ts-node": "~10.9.1", "tslib": "^2.8.1", "typescript": "~5.8.0", "zone.js": "^0.15.1"}}