{"name": "devapp-web", "version": "1.0.0", "description": "DevApp Web", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "CHROME_BIN=$(node -e \"console.log(require(\\\"puppeteer\\\").executablePath())\") ng test --watch=false --browsers=ChromeHeadless", "lint": "ng lint", "e2e": "cypress run", "build-prod": "ng build --prod", "start-prod": "ng serve --prod", "watch": "ng build --watch --configuration development", "serve-ssr": "ng run angular-app:serve-ssr", "prerender": "ng run angular-app:prerender"}, "private": true, "dependencies": {"@angular/animations": "^16.0.0", "@angular/cdk": "^16.2.14", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/core": "^16.2.12", "@angular/forms": "^16.0.0", "@angular/material": "^16.2.14", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "devapp-web": "file:", "rxjs": "^7.8.1", "tslib": "^2.6.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.14", "@angular/cli": "^16.0.0", "@angular/compiler-cli": "^16.0.0", "@types/jasmine": "~4.3.0", "@types/node": "^18.14.0", "codelyzer": "^6.0.2", "cypress": "^13.10.0", "eslint": "^8.38.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsdoc": "^40.0.0", "eslint-plugin-prefer-arrow": "^1.2.3", "jasmine-core": "~4.5.0", "jasmine-spec-reporter": "~6.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.0.0", "karma-jasmine-html-reporter": "^2.0.0", "puppeteer": "^24.10.0", "ts-node": "~10.9.1", "typescript": "~5.0.4"}}