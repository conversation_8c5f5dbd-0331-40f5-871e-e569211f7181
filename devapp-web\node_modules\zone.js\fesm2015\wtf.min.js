"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */const _global="object"==typeof window&&window||"object"==typeof self&&self||global;function patchWtf(e){let n=null,c=null;const o=function(){const e=_global.wtf;return!(!e||(n=e.trace,!n)||(c=n.events,0))}();class t{name="WTF";static forkInstance=o?c.createInstance("Zone:fork(ascii zone, ascii newZone)"):null;static scheduleInstance={};static cancelInstance={};static invokeScope={};static invokeTaskScope={};onFork(e,n,c,o){const a=e.fork(c,o);return t.forkInstance(s(c),a.name),a}onInvoke(e,o,a,r,l,i,u){const k=u||"unknown";let f=t.invokeScope[k];return f||(f=t.invokeScope[k]=c.createScope(`Zone:invoke:${u}(ascii zone)`)),n.leaveScope(f(s(a)),e.invoke(a,r,l,i,u))}onHandleError(e,n,c,o){return e.handleError(c,o)}onScheduleTask(e,n,o,r){const l=r.type+":"+r.source;let i=t.scheduleInstance[l];i||(i=t.scheduleInstance[l]=c.createInstance(`Zone:schedule:${l}(ascii zone, any data)`));const u=e.scheduleTask(o,r);return i(s(o),a(r.data,2)),u}onInvokeTask(e,o,a,r,l,i){const u=r.source;let k=t.invokeTaskScope[u];return k||(k=t.invokeTaskScope[u]=c.createScope(`Zone:invokeTask:${u}(ascii zone)`)),n.leaveScope(k(s(a)),e.invokeTask(a,r,l,i))}onCancelTask(e,n,o,r){const l=r.source;let i=t.cancelInstance[l];i||(i=t.cancelInstance[l]=c.createInstance(`Zone:cancel:${l}(ascii zone, any options)`));const u=e.cancelTask(o,r);return i(s(o),a(r.data,2)),u}}function a(e,n){if(!e||!n)return null;const c={};for(const o in e)if(e.hasOwnProperty(o)){let t=e[o];switch(typeof t){case"object":const e=t&&t.constructor&&t.constructor.name;t=e==Object.name?a(t,n-1):e;break;case"function":t=t.name||void 0}c[o]=t}return c}function s(e){let n=e.name,c=e.parent;for(;null!=c;)n=c.name+"::"+n,c=c.parent;return n}e.wtfZoneSpec=o?new t:null}patchWtf(Zone);