"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(n){"function"==typeof define&&define.amd?define(n):n()}((function(){!function n(t){t.__load_patch("mocha",(function(n,t){var e=n.Mocha;if(void 0!==e){if(void 0===t)throw new Error("Missing Zone.js");var r=t.ProxyZoneSpec,i=t.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");if(e.__zone_patch__)throw new Error('"Mocha" has already been patched with "Zone".');e.__zone_patch__=!0;var o,u,c=t.current,f=c.fork(new i("Mocha.describe")),s=null,a=c.fork(new r),p={after:n.after,afterEach:n.afterEach,before:n.before,beforeEach:n.beforeEach,describe:n.describe,it:n.it};n.describe=n.suite=function(){return p.describe.apply(this,y(arguments))},n.xdescribe=n.suite.skip=n.describe.skip=function(){return p.describe.skip.apply(this,y(arguments))},n.describe.only=n.suite.only=function(){return p.describe.only.apply(this,y(arguments))},n.it=n.specify=n.test=function(){return p.it.apply(this,l(arguments))},n.xit=n.xspecify=n.it.skip=function(){return p.it.skip.apply(this,l(arguments))},n.it.only=n.test.only=function(){return p.it.only.apply(this,l(arguments))},n.after=n.suiteTeardown=function(){return p.after.apply(this,d(arguments))},n.afterEach=n.teardown=function(){return p.afterEach.apply(this,l(arguments))},n.before=n.suiteSetup=function(){return p.before.apply(this,d(arguments))},n.beforeEach=n.setup=function(){return p.beforeEach.apply(this,l(arguments))},o=e.Runner.prototype.runTest,u=e.Runner.prototype.run,e.Runner.prototype.runTest=function(n){var e=this;t.current.scheduleMicroTask("mocha.forceTask",(function(){o.call(e,n)}))},e.Runner.prototype.run=function(n){return this.on("test",(function(n){s=c.fork(new r)})),this.on("fail",(function(n,t){var e=s&&s.get("ProxyZoneSpec");if(e&&t)try{t.message+=e.getAndClearPendingTasksInfo()}catch(n){}})),u.call(this,n)}}function h(n,t,e){for(var r=function(r){var i=n[r];"function"==typeof i&&(n[r]=0===i.length?t(i):e(i),n[r].toString=function(){return i.toString()})},i=0;i<n.length;i++)r(i);return n}function y(n){return h(n,(function(n){return function(){return f.run(n,this,arguments)}}))}function l(n){return h(n,(function(n){return function(){return s.run(n,this)}}),(function(n){return function(t){return s.run(n,this,[t])}}))}function d(n){return h(n,(function(n){return function(){return a.run(n,this)}}),(function(n){return function(t){return a.run(n,this,[t])}}))}}))}(Zone)}));