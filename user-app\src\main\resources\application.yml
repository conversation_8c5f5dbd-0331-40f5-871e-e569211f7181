spring:
  application:
    name: user-app
  profiles:
    active: dev
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: group_id

app:
  security:
    user: admin
    password: password

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: jdbc:h2:mem:userdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  h2:
    console:
      enabled: true
      path: /h2-console
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  sql:
    init:
      mode: never

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ${DB_URL:***************************************}
    driver-class-name: ${DB_DRIVER:org.postgresql.Driver}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate
    show-sql: false
  sql:
    init:
      mode: never
