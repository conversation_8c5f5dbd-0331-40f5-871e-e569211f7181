"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("rxjs")):"function"==typeof define&&define.amd?define(["rxjs"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).rxjs)}(this,(function(e){!function t(r){r.__load_patch("rxjs",(function(t,r,n){var i,o,u,s,c,b=r.__symbol__,a=Object.defineProperties;n.patchMethod(e.Observable.prototype,"lift",(function(e){return function(t,i){var o=e.apply(t,i);return o.operator&&(o.operator._zone=r.current,n.patchMethod(o.operator,"call",(function(e){return function(t,n){return t._zone&&t._zone!==r.current?t._zone.run(e,t,n):e.apply(t,n)}}))),o}})),o=(i=e.Observable.prototype)[b("_subscribe")]=i._subscribe,a(e.Observable.prototype,{_zone:{value:null,writable:!0,configurable:!0},_zoneSource:{value:null,writable:!0,configurable:!0},_zoneSubscribe:{value:null,writable:!0,configurable:!0},source:{configurable:!0,get:function(){return this._zoneSource},set:function(e){this._zone=r.current,this._zoneSource=e}},_subscribe:{configurable:!0,get:function(){if(this._zoneSubscribe)return this._zoneSubscribe;if(this.constructor===e.Observable)return o;var t=Object.getPrototypeOf(this);return t&&t._subscribe},set:function(e){this._zone=r.current,this._zoneSubscribe=e?function(){if(this._zone&&this._zone!==r.current){var t=this._zone.run(e,this,arguments);if("function"==typeof t){var n=this._zone;return function(){return n!==r.current?n.run(t,this,arguments):t.apply(this,arguments)}}return t}return e.apply(this,arguments)}:e}},subjectFactory:{get:function(){return this._zoneSubjectFactory},set:function(e){var t=this._zone;this._zoneSubjectFactory=function(){return t&&t!==r.current?t.run(e,this,arguments):e.apply(this,arguments)}}}}),a(e.Subscription.prototype,{_zone:{value:null,writable:!0,configurable:!0},_zoneUnsubscribe:{value:null,writable:!0,configurable:!0},_unsubscribe:{get:function(){if(this._zoneUnsubscribe||this._zoneUnsubscribeCleared)return this._zoneUnsubscribe;var e=Object.getPrototypeOf(this);return e&&e._unsubscribe},set:function(e){this._zone=r.current,e?(this._zoneUnsubscribeCleared=!1,this._zoneUnsubscribe=function(){return this._zone&&this._zone!==r.current?this._zone.run(e,this,arguments):e.apply(this,arguments)}):(this._zoneUnsubscribe=e,this._zoneUnsubscribeCleared=!0)}}}),u=e.Subscriber.prototype.next,s=e.Subscriber.prototype.error,c=e.Subscriber.prototype.complete,Object.defineProperty(e.Subscriber.prototype,"destination",{configurable:!0,get:function(){return this._zoneDestination},set:function(e){this._zone=r.current,this._zoneDestination=e}}),e.Subscriber.prototype.next=function(){var e=this._zone;return e&&e!==r.current?e.run(u,this,arguments,"rxjs.Subscriber.next"):u.apply(this,arguments)},e.Subscriber.prototype.error=function(){var e=this._zone;return e&&e!==r.current?e.run(s,this,arguments,"rxjs.Subscriber.error"):s.apply(this,arguments)},e.Subscriber.prototype.complete=function(){var e=this._zone;return e&&e!==r.current?e.run(c,this,arguments,"rxjs.Subscriber.complete"):c.call(this)}}))}(Zone)}));