{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./order.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./order.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { OrderService } from '../services/order.service';\nlet OrderComponent = class OrderComponent {\n  constructor(orderService) {\n    this.orderService = orderService;\n    this.orders = [];\n    this.newOrder = this.initOrder();\n  }\n  ngOnInit() {\n    this.orderService.getAllOrders().subscribe(data => {\n      this.orders = data;\n    });\n  }\n  createOrder() {\n    this.orderService.createOrder(this.newOrder).subscribe(order => {\n      this.orders.push(order);\n      this.newOrder = this.initOrder();\n    });\n  }\n  initOrder() {\n    return {\n      user: {\n        id: 0,\n        name: ''\n      },\n      productId: 0\n    };\n  }\n  static #_ = this.ctorParameters = () => [{\n    type: OrderService\n  }];\n};\nOrderComponent = __decorate([Component({\n  selector: 'app-order',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  styles: [__NG_CLI_RESOURCE__1]\n})], OrderComponent);\nexport { OrderComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "OrderService", "OrderComponent", "constructor", "orderService", "orders", "newOrder", "initOrder", "ngOnInit", "getAllOrders", "subscribe", "data", "createOrder", "order", "push", "user", "id", "name", "productId", "_", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\dev\\workspace\\devapp\\devapp-web\\src\\app\\order\\order.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { OrderService } from '../services/order.service';\r\nimport { Order } from '../models/order.model';\r\n\r\n@Component({\r\n  selector: 'app-order',\r\n  templateUrl: './order.component.html',\r\n  styleUrls: ['./order.component.css'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule]\r\n})\r\nexport class OrderComponent implements OnInit {\r\n  orders: Order[] = [];\r\n  newOrder: Order = this.initOrder();\r\n\r\n  constructor(private orderService: OrderService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.orderService.getAllOrders().subscribe(data => {\r\n      this.orders = data;\r\n    });\r\n  }\r\n\r\n  createOrder(): void {\r\n    this.orderService.createOrder(this.newOrder).subscribe(order => {\r\n      this.orders.push(order);\r\n      this.newOrder = this.initOrder();\r\n    });\r\n  }\r\n\r\n  initOrder(): Order {\r\n    return { user: { id: 0, name: '' }, productId: 0 };\r\n  }\r\n}"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,2BAA2B;AAUjD,IAAMC,cAAc,GAApB,MAAMA,cAAc;EAIzBC,YAAoBC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IAHhC,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,QAAQ,GAAU,IAAI,CAACC,SAAS,EAAE;EAEgB;EAElDC,QAAQA,CAAA;IACN,IAAI,CAACJ,YAAY,CAACK,YAAY,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MAChD,IAAI,CAACN,MAAM,GAAGM,IAAI;IACpB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,YAAY,CAACQ,WAAW,CAAC,IAAI,CAACN,QAAQ,CAAC,CAACI,SAAS,CAACG,KAAK,IAAG;MAC7D,IAAI,CAACR,MAAM,CAACS,IAAI,CAACD,KAAK,CAAC;MACvB,IAAI,CAACP,QAAQ,GAAG,IAAI,CAACC,SAAS,EAAE;IAClC,CAAC,CAAC;EACJ;EAEAA,SAASA,CAAA;IACP,OAAO;MAAEQ,IAAI,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAE;MAAEC,SAAS,EAAE;IAAC,CAAE;EACpD;EAAC,QAAAC,CAAA,G;;;;AArBUjB,cAAc,GAAAkB,UAAA,EAP1BtB,SAAS,CAAC;EACTuB,QAAQ,EAAE,WAAW;EACrBC,QAAA,EAAAC,oBAAqC;EAErCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC1B,YAAY,EAAEC,WAAW,CAAC;;CACrC,CAAC,C,EACWE,cAAc,CAsB1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}