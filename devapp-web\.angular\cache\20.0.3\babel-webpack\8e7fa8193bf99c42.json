{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { OrderService } from '../services/order.service';\nimport { FormsModule } from '@angular/forms';\nimport { OrderComponent } from './order.component';\ndescribe('OrderComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule, FormsModule, OrderComponent],\n      providers: [OrderService]\n    });\n    fixture = TestBed.createComponent(OrderComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "HttpClientTestingModule", "OrderService", "FormsModule", "OrderComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "providers", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\dev\\workspace\\devapp\\devapp-web\\src\\app\\order\\order.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\r\nimport { OrderService } from '../services/order.service';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { OrderComponent } from './order.component';\r\n\r\ndescribe('OrderComponent', () => {\r\n  let component: OrderComponent;\r\n  let fixture: ComponentFixture<OrderComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      imports: [HttpClientTestingModule, FormsModule, OrderComponent],\r\n      providers: [OrderService]\r\n    });\r\n    fixture = TestBed.createComponent(OrderComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,mBAAmB;AAElDC,QAAQ,CAAC,gBAAgB,EAAE,MAAK;EAC9B,IAAIC,SAAyB;EAC7B,IAAIC,OAAyC;EAE7CC,UAAU,CAAC,MAAK;IACdR,OAAO,CAACS,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACT,uBAAuB,EAAEE,WAAW,EAAEC,cAAc,CAAC;MAC/DO,SAAS,EAAE,CAACT,YAAY;KACzB,CAAC;IACFK,OAAO,GAAGP,OAAO,CAACY,eAAe,CAACR,cAAc,CAAC;IACjDE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}