"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */class ProxyZoneSpec{defaultSpecDelegate;name="ProxyZone";_delegateSpec=null;properties={ProxyZoneSpec:this};propertyKeys=null;lastTaskState=null;isNeedToTriggerHasTask=!1;tasks=[];static get(){return Zone.current.get("ProxyZoneSpec")}static isLoaded(){return ProxyZoneSpec.get()instanceof ProxyZoneSpec}static assertPresent(){const e=ProxyZoneSpec.get();if(void 0===e)throw new Error("Expected to be running in 'ProxyZone', but it was not found.");return e}constructor(e=null){this.defaultSpecDelegate=e,this.setDelegate(e)}setDelegate(e){const t=this._delegateSpec!==e;this._delegateSpec=e,this.propertyKeys&&this.propertyKeys.forEach((e=>delete this.properties[e])),this.propertyKeys=null,e&&e.properties&&(this.propertyKeys=Object.keys(e.properties),this.propertyKeys.forEach((t=>this.properties[t]=e.properties[t]))),t&&this.lastTaskState&&(this.lastTaskState.macroTask||this.lastTaskState.microTask)&&(this.isNeedToTriggerHasTask=!0)}getDelegate(){return this._delegateSpec}resetDelegate(){this.getDelegate(),this.setDelegate(this.defaultSpecDelegate)}tryTriggerHasTask(e,t,s){this.isNeedToTriggerHasTask&&this.lastTaskState&&(this.isNeedToTriggerHasTask=!1,this.onHasTask(e,t,s,this.lastTaskState))}removeFromTasks(e){if(this.tasks)for(let t=0;t<this.tasks.length;t++)if(this.tasks[t]===e)return void this.tasks.splice(t,1)}getAndClearPendingTasksInfo(){if(0===this.tasks.length)return"";const e="--Pending async tasks are: ["+this.tasks.map((e=>{const t=e.data&&Object.keys(e.data).map((t=>t+":"+e.data[t])).join(",");return`type: ${e.type}, source: ${e.source}, args: {${t}}`}))+"]";return this.tasks=[],e}onFork(e,t,s,a){return this._delegateSpec&&this._delegateSpec.onFork?this._delegateSpec.onFork(e,t,s,a):e.fork(s,a)}onIntercept(e,t,s,a,r){return this._delegateSpec&&this._delegateSpec.onIntercept?this._delegateSpec.onIntercept(e,t,s,a,r):e.intercept(s,a,r)}onInvoke(e,t,s,a,r,o,n){return this.tryTriggerHasTask(e,t,s),this._delegateSpec&&this._delegateSpec.onInvoke?this._delegateSpec.onInvoke(e,t,s,a,r,o,n):e.invoke(s,a,r,o,n)}onHandleError(e,t,s,a){return this._delegateSpec&&this._delegateSpec.onHandleError?this._delegateSpec.onHandleError(e,t,s,a):e.handleError(s,a)}onScheduleTask(e,t,s,a){return"eventTask"!==a.type&&this.tasks.push(a),this._delegateSpec&&this._delegateSpec.onScheduleTask?this._delegateSpec.onScheduleTask(e,t,s,a):e.scheduleTask(s,a)}onInvokeTask(e,t,s,a,r,o){return"eventTask"!==a.type&&this.removeFromTasks(a),this.tryTriggerHasTask(e,t,s),this._delegateSpec&&this._delegateSpec.onInvokeTask?this._delegateSpec.onInvokeTask(e,t,s,a,r,o):e.invokeTask(s,a,r,o)}onCancelTask(e,t,s,a){return"eventTask"!==a.type&&this.removeFromTasks(a),this.tryTriggerHasTask(e,t,s),this._delegateSpec&&this._delegateSpec.onCancelTask?this._delegateSpec.onCancelTask(e,t,s,a):e.cancelTask(s,a)}onHasTask(e,t,s,a){this.lastTaskState=a,this._delegateSpec&&this._delegateSpec.onHasTask?this._delegateSpec.onHasTask(e,t,s,a):e.hasTask(s,a)}}function patchProxyZoneSpec(e){e.ProxyZoneSpec=ProxyZoneSpec}patchProxyZoneSpec(Zone);