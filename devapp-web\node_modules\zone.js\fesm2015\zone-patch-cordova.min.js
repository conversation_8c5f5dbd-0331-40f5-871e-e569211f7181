"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */function patchCordova(e){e.__load_patch("cordova",((e,o,r)=>{if(e.cordova){const t="cordova.exec.success",a="cordova.exec.error",c="function",d=r.patchMethod(e.cordova,"exec",(()=>function(e,r){return r.length>0&&typeof r[0]===c&&(r[0]=o.current.wrap(r[0],t)),r.length>1&&typeof r[1]===c&&(r[1]=o.current.wrap(r[1],a)),d.apply(e,r)}))}})),e.__load_patch("cordova.FileReader",((e,o)=>{e.cordova&&void 0!==e.FileReader&&document.addEventListener("deviceReady",(()=>{const r=e.FileReader;["abort","error","load","loadstart","loadend","progress"].forEach((e=>{const t=o.__symbol__("ON_PROPERTY"+e);Object.defineProperty(r.prototype,t,{configurable:!0,get:function(){return this._realReader&&this._realReader[t]}})}))}))}))}patchCordova(Zone);