"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(n){"function"==typeof define&&define.amd?define(n):n()}((function(){var n="object"==typeof window&&window||"object"==typeof self&&self||global;!function e(o){var c,t=null,a=null,r=!(!(c=n.wtf)||!(t=c.trace)||(a=t.events,0)),i=function(){function n(){this.name="WTF"}return n.prototype.onFork=function(e,o,c,t){var a=e.fork(c,t);return n.forkInstance(u(c),a.name),a},n.prototype.onInvoke=function(e,o,c,r,i,s,f){var l=f||"unknown",p=n.invokeScope[l];return p||(p=n.invokeScope[l]=a.createScope("Zone:invoke:".concat(f,"(ascii zone)"))),t.leaveScope(p(u(c)),e.invoke(c,r,i,s,f))},n.prototype.onHandleError=function(n,e,o,c){return n.handleError(o,c)},n.prototype.onScheduleTask=function(e,o,c,t){var r=t.type+":"+t.source,i=n.scheduleInstance[r];i||(i=n.scheduleInstance[r]=a.createInstance("Zone:schedule:".concat(r,"(ascii zone, any data)")));var f=e.scheduleTask(c,t);return i(u(c),s(t.data,2)),f},n.prototype.onInvokeTask=function(e,o,c,r,i,s){var f=r.source,l=n.invokeTaskScope[f];return l||(l=n.invokeTaskScope[f]=a.createScope("Zone:invokeTask:".concat(f,"(ascii zone)"))),t.leaveScope(l(u(c)),e.invokeTask(c,r,i,s))},n.prototype.onCancelTask=function(e,o,c,t){var r=t.source,i=n.cancelInstance[r];i||(i=n.cancelInstance[r]=a.createInstance("Zone:cancel:".concat(r,"(ascii zone, any options)")));var f=e.cancelTask(c,t);return i(u(c),s(t.data,2)),f},n.forkInstance=r?a.createInstance("Zone:fork(ascii zone, ascii newZone)"):null,n.scheduleInstance={},n.cancelInstance={},n.invokeScope={},n.invokeTaskScope={},n}();function s(n,e){if(!n||!e)return null;var o={};for(var c in n)if(n.hasOwnProperty(c)){var t=n[c];switch(typeof t){case"object":var a=t&&t.constructor&&t.constructor.name;t=a==Object.name?s(t,e-1):a;break;case"function":t=t.name||void 0}o[c]=t}return o}function u(n){for(var e=n.name,o=n.parent;null!=o;)e=o.name+"::"+e,o=o.parent;return e}o.wtfZoneSpec=r?new i:null}(Zone)}));