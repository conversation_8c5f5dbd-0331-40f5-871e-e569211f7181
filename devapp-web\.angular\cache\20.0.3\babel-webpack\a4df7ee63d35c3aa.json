{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { UserService } from '../services/user.service';\nimport { FormsModule } from '@angular/forms';\nimport { UserComponent } from './user.component';\ndescribe('UserComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule, FormsModule, UserComponent],\n      providers: [UserService]\n    });\n    fixture = TestBed.createComponent(UserComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "HttpClientTestingModule", "UserService", "FormsModule", "UserComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "providers", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\dev\\workspace\\devapp\\devapp-web\\src\\app\\user\\user.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\r\nimport { UserService } from '../services/user.service';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { UserComponent } from './user.component';\r\n\r\ndescribe('UserComponent', () => {\r\n  let component: UserComponent;\r\n  let fixture: ComponentFixture<UserComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      imports: [HttpClientTestingModule, FormsModule, UserComponent],\r\n      providers: [UserService]\r\n    });\r\n    fixture = TestBed.createComponent(UserComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,aAAa,QAAQ,kBAAkB;AAEhDC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,SAAwB;EAC5B,IAAIC,OAAwC;EAE5CC,UAAU,CAAC,MAAK;IACdR,OAAO,CAACS,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACT,uBAAuB,EAAEE,WAAW,EAAEC,aAAa,CAAC;MAC9DO,SAAS,EAAE,CAACT,WAAW;KACxB,CAAC;IACFK,OAAO,GAAGP,OAAO,CAACY,eAAe,CAACR,aAAa,CAAC;IAChDE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}