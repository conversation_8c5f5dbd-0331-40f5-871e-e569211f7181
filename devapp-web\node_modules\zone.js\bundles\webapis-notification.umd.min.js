"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){!function t(o){o.__load_patch("notification",(function(t,o,n){var e=t.Notification;if(e&&e.prototype){var i=Object.getOwnPropertyDescriptor(e.prototype,"onerror");i&&i.configurable&&n.patchOnProperties(e.prototype,null)}}))}(Zone)}));